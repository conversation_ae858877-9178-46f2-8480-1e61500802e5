name: "Build & push devcontainer"

on:
  push:
    paths:
      - .devcontainer/**
      - .github/workflows/build_images.yml

permissions:
  contents: read
  packages: write # Required for GHCR

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Check out current commit
        uses: actions/checkout@v4

      - name: Build and devcontainer build image
        uses: getsentry/action-build-and-push-images@main
        with:
          ghcr_image_name: 'sentry-ruby-devcontainer-build'
          dockerfile_path: '.devcontainer/Dockerfile'
          ghcr: true

      - name: Use outputs
        run: |
          echo "GHCR URL: ${{ steps.build.outputs.ghcr_image_url }}"
